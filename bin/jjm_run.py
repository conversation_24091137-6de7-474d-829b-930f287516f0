"""Executable driver script for Jenkins Job Manager.

This script serves as a entry point that delegates to the main
CLI application. It can be used as a standalone executable or called
directly from the command line.
"""

import sys
from pathlib import Path

# Add the package directory to Python path if running from source
script_dir = Path(__file__).parent
package_dir = script_dir.parent
if package_dir not in sys.path:
    sys.path.insert(0, str(package_dir))

try:
    from jenkins_job_manager.cli import app

    if __name__ == "__main__":
        app()

except ImportError as e:
    print(f"Error: Failed to import jenkins_job_manager: {e}", file=sys.stderr)
    print("Make sure the package is installed or you're running from the correct directory.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"Error: {e}", file=sys.stderr)
    sys.exit(1)
