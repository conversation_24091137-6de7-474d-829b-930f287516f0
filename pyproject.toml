[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jenkins-job-manager"
version = "0.1.0"
description = "A Python package for managing Jenkins jobs via CLI"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Len<PERSON><PERSON> Voelz", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Build Tools",
    "Topic :: System :: Systems Administration",
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "httpx>=0.24.0",
    "pyyaml>=6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-httpx>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "mypy>=1.0.0",
    "ruff>=0.1.0",
    "pdoc>=14.0.0",
    "types-pyyaml",
    "types-requests",
]

[project.scripts]
jjm = "jenkins_job_manager.cli:app"

[project.urls]
Homepage = "https://github.com/lennartvoelz/jenkins-job-manager"
Repository = "https://github.com/lennartvoelz/jenkins-job-manager"
Issues = "https://github.com/lennartvoelz/jenkins-job-manager/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["jenkins_job_manager*"]

[tool.ruff]
target-version = "py38"
line-length = 88

[tool.ruff.lint]
select = ["I", "S", "UP", "E", "F", "W"]
ignore = ["E501"]  # Line too long (handled by formatter)

[tool.ruff.lint.isort]
known-first-party = ["jenkins_job_manager"]

[tool.mypy]
python_version = "3.8"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --cov=jenkins_job_manager --cov-report=term-missing --cov-report=html"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["jenkins_job_manager"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
