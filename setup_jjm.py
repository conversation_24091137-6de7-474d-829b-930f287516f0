#!/usr/bin/env python3
import os
import shutil
import sys
from pathlib import Path

import yaml


def create_config_directory():
    """Create the conf/ directory with example config files."""
    conf_dir = Path("conf")
    conf_dir.mkdir(exist_ok=True)
    
    # Create example query_config.yml
    query_config = {
        "query": {
            "database": "production_db",
            "table": "user_events",
            "date_range": {
                "start": "2024-01-01",
                "end": "2024-12-31"
            },
            "filters": {
                "event_type": ["click", "view", "purchase"],
                "user_segment": "active_users"
            },
            "aggregation": {
                "group_by": ["user_id", "event_type"],
                "metrics": ["count", "sum", "avg"]
            },
            "performance": {
                "limit": 1000000,
                "timeout": 300
            }
        }
    }
    
    # Create example sp_config.yml
    sp_config = {
        "sp": {
            "procedure_name": "process_user_data",
            "parameters": {
                "batch_size": 10000,
                "parallel_workers": 4,
                "data_validation": True
            },
            "output": {
                "format": "parquet",
                "compression": "snappy",
                "partitioning": ["date", "user_segment"]
            },
            "quality_checks": {
                "null_threshold": 0.05,
                "duplicate_threshold": 0.01,
                "completeness_threshold": 0.95
            },
            "monitoring": {
                "enable_metrics": True,
                "alert_on_failure": True
            }
        }
    }
    
    # Create example training_config.yml
    training_config = {
        "training": {
            "algorithm": "xgboost",
            "hyperparameters": {
                "n_estimators": 100,
                "max_depth": 6,
                "learning_rate": 0.1,
                "subsample": 0.8
            },
            "validation": {
                "method": "cross_validation",
                "folds": 5,
                "test_size": 0.2
            },
            "features": {
                "categorical": ["user_segment", "event_type"],
                "numerical": ["count", "sum", "avg"],
                "target": "conversion_rate"
            }
        },
        "model": {
            "name": "user_conversion_model",
            "version": "v1.0",
            "artifact_path": "/models/user_conversion"
        },
        "data": {
            "input_path": "/data/processed/user_events",
            "output_path": "/data/models/user_conversion",
            "preprocessing": {
                "scaling": "standard",
                "encoding": "one_hot"
            }
        }
    }
    
    # Write config files
    configs = [
        ("query_config.yml", query_config),
        ("sp_config.yml", sp_config),
        ("training_config.yml", training_config)
    ]
    
    for filename, config in configs:
        config_path = conf_dir / filename
        if not config_path.exists():
            with config_path.open("w") as f:
                yaml.dump(config, f, default_flow_style=False, indent=2)
            print(f"✅ Created {config_path}")
        else:
            print(f"⚠️  {config_path} already exists, skipping")


def create_gitignore():
    """Create or update .gitignore to exclude JJM state files."""
    gitignore_path = Path(".gitignore")
    
    jjm_entries = [
        "# Jenkins Job Manager configuration directory",
        "conf/",
        "",
    ]
    
    if gitignore_path.exists():
        content = gitignore_path.read_text()
        if "Jenkins Job Manager" not in content:
            with gitignore_path.open("a") as f:
                f.write("\n" + "\n".join(jjm_entries))
            print("✅ Updated .gitignore with JJM state file exclusions")
        else:
            print("⚠️  .gitignore already contains JJM entries")
    else:
        with gitignore_path.open("w") as f:
            f.write("\n".join(jjm_entries))
        print("✅ Created .gitignore with JJM state file exclusions")


def setup_environment_template():
    """Create environment template file."""
    env_template = Path(".env.template")
    
    template_content = """# Jenkins Job Manager Environment Variables
# Copy this file to .env and fill in your actual values

# Jenkins Authentication
JENKINS_USER=your-jenkins-username
JENKINS_API_TOKEN=your-jenkins-api-token

# Optional: Custom Jenkins timeout (default: 30 seconds)
# JENKINS_TIMEOUT=30

# Optional: Custom state file location
# JJM_STATE_FILE=/path/to/custom/state.json
"""
    
    if not env_template.exists():
        env_template.write_text(template_content)
        print("✅ Created .env.template for environment variables")
    else:
        print("⚠️  .env.template already exists, skipping")


def initialize_default_configs():
    """Initialize default configuration states."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        conf_dir = Path("conf")
        if not conf_dir.exists():
            print("❌ conf/ directory not found. Run config setup first.")
            return False

        # Check if all required config files exist
        required_configs = ["query_config.yml", "sp_config.yml", "training_config.yml"]
        missing_configs = []

        for config_file in required_configs:
            if not (conf_dir / config_file).exists():
                missing_configs.append(config_file)

        if missing_configs:
            print(f"❌ Missing config files: {missing_configs}")
            return False

        # Initialize defaults
        service = JenkinsService(config_dir=str(conf_dir))
        service.init_defaults()

        return True

    except ImportError as e:
        print(f"❌ Failed to import JJM modules: {e}")
        print("   Make sure the package is installed: pip install -e .")
        return False
    except Exception as e:
        print(f"❌ Failed to initialize default configs: {e}")
        return False


def setup_jjm_config():
    """Setup JJM configuration."""
    try:
        from jenkins_job_manager.config.jjm_config import load_config, get_config_file

        config = load_config()  # This will create default config if not exists
        config_file = get_config_file()

        print("✅ Initialized JJM configuration")
        print(f"📁 Config file location: {config_file}")
        print(f"🎯 Jenkins URL: {config.get('jenkins_url', 'http://localhost:8080')}")

        return True

    except ImportError as e:
        print(f"❌ Failed to import JJM modules: {e}")
        print("   Make sure the package is installed: pip install -e .")
        return False
    except Exception as e:
        print(f"❌ Failed to setup JJM config: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Setting up Jenkins Job Manager (JJM)")
    print("=" * 50)
    
    # Create config directory and files
    print("\n📁 Setting up configuration files...")
    create_config_directory()
    
    # Setup .gitignore
    print("\n🔒 Setting up .gitignore...")
    create_gitignore()
    
    # Create environment template
    print("\n🌍 Setting up environment template...")
    setup_environment_template()
    
    # Setup JJM configuration
    print("\n⚙️  Setting up JJM configuration...")
    jjm_config_success = setup_jjm_config()

    # Initialize default configs
    print("\n⚙️  Initializing default configuration states...")
    default_config_success = initialize_default_configs()

    if jjm_config_success and default_config_success:
        print("\n✅ JJM setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Customize the config files in conf/ directory as needed")
        print("   2. Run 'jjm init-defaults' to restore the default configs")
        print("   3. Use 'jjm trigger' to execute jobs")
        print("\n🔧 Configuration commands:")
        print("   jjm config show                           # Show current configuration")
        print("   jjm config set jenkins_url <url>          # Set Jenkins URL")
        print("   jjm config set dataset_job <job-name>     # Set dataset job name")
    else:
        print("\n⚠️  Setup completed with warnings. Please install the package first:")
        print("   pip install -e .")
        print("   Then run this script again or manually run 'jjm init-defaults'")


if __name__ == "__main__":
    main()
