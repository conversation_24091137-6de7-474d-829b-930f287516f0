#!/bin/bash

# Jenkins Job Manager - Confluence Documentation Update Script
# This script pushes the updated JJM documentation to Confluence

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Jenkins Job Manager - Confluence Update${NC}"
echo "=================================================="

# Check if required environment variables are set
if [ -z "$CONFLUENCE_USERNAME" ]; then
    echo -e "${RED}❌ Error: CONFLUENCE_USERNAME environment variable not set${NC}"
    echo "Please set it with: export CONFLUENCE_USERNAME='your-username'"
    exit 1
fi

if [ -z "$CONFLUENCE_API_TOKEN" ]; then
    echo -e "${RED}❌ Error: CONFLUENCE_API_TOKEN environment variable not set${NC}"
    echo "Please set it with: export CONFLUENCE_API_TOKEN='your-api-token'"
    exit 1
fi

if [ -z "$CONFLUENCE_URL" ]; then
    echo -e "${RED}❌ Error: CONFLUENCE_URL environment variable not set${NC}"
    echo "Please set it with: export CONFLUENCE_URL='https://your-confluence.com'"
    exit 1
fi

if [ -z "$CONFLUENCE_SPACE_KEY" ]; then
    echo -e "${RED}❌ Error: CONFLUENCE_SPACE_KEY environment variable not set${NC}"
    echo "Please set it with: export CONFLUENCE_SPACE_KEY='YOUR_SPACE'"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables configured${NC}"
echo "   Username: $CONFLUENCE_USERNAME"
echo "   URL: $CONFLUENCE_URL"
echo "   Space: $CONFLUENCE_SPACE_KEY"
echo ""

# Get the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${YELLOW}📖 Updating Confluence documentation...${NC}"

# Activate virtual environment if it exists
if [ -d "$PROJECT_ROOT/venv" ]; then
    echo "🔧 Activating virtual environment..."
    source "$PROJECT_ROOT/venv/bin/activate"
fi

# Run the Confluence update script
python "$SCRIPT_DIR/update_confluence.py" \
    --confluence-url "$CONFLUENCE_URL" \
    --space-key "$CONFLUENCE_SPACE_KEY" \
    --page-title "Jenkins Job Manager Documentation" \
    --markdown-file "$SCRIPT_DIR/confluence_update.md"

echo ""
echo -e "${GREEN}✅ Documentation update completed!${NC}"
echo -e "${BLUE}📄 View the updated documentation at:${NC}"
echo "   $CONFLUENCE_URL/display/$CONFLUENCE_SPACE_KEY"
echo ""
echo -e "${YELLOW}💡 Tip: Bookmark the page for easy access${NC}"
