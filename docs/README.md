# Jenkins Job Manager Documentation

This directory contains the documentation generation and publishing tools for Jenkins Job Manager.

## Files Overview

- `confluence_update.md` - Main documentation content in Markdown format
- `update_confluence.py` - Python script to convert Markdown to Confluence format and push
- `push_to_confluence.sh` - Bash script wrapper for easy Confluence updates
- `generate_docs.py` - Generates API documentation using pdoc
- `confluence_push.py` - Core Confluence API integration
- `html/` - Generated HTML documentation

## Quick Start

### 1. Update Confluence Documentation

Set up environment variables:
```bash
export CONFLUENCE_USERNAME="your-username"
export CONFLUENCE_API_TOKEN="your-api-token"
export CONFLUENCE_URL="https://your-confluence.com"
export CONFLUENCE_SPACE_KEY="YOUR_SPACE"
```

Run the update:
```bash
./docs/push_to_confluence.sh
```

### 2. Generate API Documentation

```bash
python docs/generate_docs.py
```

This generates HTML documentation in `docs/html/` using pdoc.

## Documentation Content

The main documentation (`confluence_update.md`) covers:

### Recent Updates (v2.0)
- **Enhanced Security**: Environment-only authentication
- **Advanced Job Management**: Sequential execution, tracking, cancellation
- **Simplified API**: Removed parameters, cleaner interface

### Key Features
- **Sequential Pipeline Execution**: Dataset → Training workflow
- **Real-time Job Tracking**: Status monitoring with unique job IDs
- **Job Cancellation**: Cancel running/queued jobs
- **Configuration Management**: Multi-file config merging
- **Comprehensive Testing**: 86 tests with 73% coverage

### Usage Examples
- Basic commands (`jjm trigger`, `jjm status`, `jjm cancel`)
- Configuration setup
- Error handling and troubleshooting

## Updating Documentation

### To Update Content
1. Edit `confluence_update.md` with your changes
2. Run `./docs/push_to_confluence.sh` to publish

### To Update API Docs
1. Update docstrings in the code
2. Run `python docs/generate_docs.py`
3. Optionally push to Confluence with the API docs

## Environment Variables

### Required for Confluence Updates
- `CONFLUENCE_USERNAME` - Your Confluence username
- `CONFLUENCE_API_TOKEN` - Your Confluence API token
- `CONFLUENCE_URL` - Your Confluence base URL
- `CONFLUENCE_SPACE_KEY` - Target Confluence space

### Optional
- `CONFLUENCE_PAGE_TITLE` - Custom page title (default: "Jenkins Job Manager Documentation")

## Scripts Reference

### `update_confluence.py`
Converts Markdown to Confluence format and pushes to server.

Usage:
```bash
python docs/update_confluence.py \
    --confluence-url "https://confluence.example.com" \
    --space-key "DEV" \
    --page-title "JJM Documentation"
```

### `push_to_confluence.sh`
Wrapper script that:
- Validates environment variables
- Activates virtual environment
- Runs the update script
- Provides user-friendly output

### `generate_docs.py`
Generates API documentation using pdoc:
- Extracts docstrings from code
- Creates HTML documentation
- Includes source code links

## Troubleshooting

### Common Issues

#### Authentication Failed
- Verify `CONFLUENCE_USERNAME` and `CONFLUENCE_API_TOKEN`
- Check if API token has proper permissions

#### Page Not Found
- Verify `CONFLUENCE_SPACE_KEY` exists
- Check if you have write permissions to the space

#### Dependencies Missing
```bash
pip install requests beautifulsoup4 markdown pdoc
```

#### Permission Denied
```bash
chmod +x docs/push_to_confluence.sh
```

## Maintenance

### Regular Updates
1. Update documentation when features change
2. Regenerate API docs after code changes
3. Keep Confluence content synchronized

### Version Control
- All documentation source files are version controlled
- Generated HTML files can be ignored in `.gitignore`
- Confluence content is backed up in Markdown format

## Integration

### CI/CD Pipeline
Consider adding documentation updates to your CI/CD pipeline:

```yaml
# Example GitHub Actions step
- name: Update Documentation
  run: |
    export CONFLUENCE_USERNAME="${{ secrets.CONFLUENCE_USERNAME }}"
    export CONFLUENCE_API_TOKEN="${{ secrets.CONFLUENCE_API_TOKEN }}"
    export CONFLUENCE_URL="${{ secrets.CONFLUENCE_URL }}"
    export CONFLUENCE_SPACE_KEY="${{ secrets.CONFLUENCE_SPACE_KEY }}"
    ./docs/push_to_confluence.sh
```

### Automated Updates
The documentation system supports automated updates when:
- Code changes are merged
- New releases are created
- Configuration changes are made

This ensures documentation stays current with the codebase.
