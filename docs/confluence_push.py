#!/usr/bin/env python3
import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup


class ConfluencePusher:
    """Handles pushing documentation to Confluence."""
    
    def __init__(self, base_url: str, username: str, api_token: str, space_key: str) -> None:
        """Initialize Confluence pusher.
        
        Args:
            base_url: Confluence base URL
            username: Confluence username
            api_token: Confluence API token
            space_key: Confluence space key
        """
        self.base_url = base_url.rstrip("/")
        self.username = username
        self.api_token = api_token
        self.space_key = space_key
        self.session = requests.Session()
        self.session.auth = (username, api_token)
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json",
        })
    
    def convert_html_to_confluence(self, html_content: str) -> str:
        """Convert HTML content to Confluence storage format.
        
        Args:
            html_content: HTML content to convert
            
        Returns:
            Confluence-compatible content
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style tags
        for tag in soup(["script", "style"]):
            tag.decompose()
        
        # Convert code blocks to Confluence code macro
        for pre in soup.find_all('pre'):
            code_content = pre.get_text()
            language = "python"  # Default to Python
            
            # Try to detect language from class
            if pre.get('class'):
                for cls in pre.get('class'):
                    if cls.startswith('language-'):
                        language = cls.replace('language-', '')
                        break
            
            # Create Confluence code macro
            code_macro = soup.new_tag('ac:structured-macro')
            code_macro['ac:name'] = 'code'
            
            # Add language parameter
            param = soup.new_tag('ac:parameter')
            param['ac:name'] = 'language'
            param.string = language
            code_macro.append(param)
            
            # Add code content
            body = soup.new_tag('ac:plain-text-body')
            body.string = code_content
            code_macro.append(body)
            
            pre.replace_with(code_macro)
        
        # Convert headers to Confluence format
        for i in range(1, 7):
            for header in soup.find_all(f'h{i}'):
                header.name = 'h1' if i <= 3 else 'h2'  # Confluence has limited header levels
        
        # Clean up and return
        return str(soup)
    
    def get_page_id(self, title: str) -> Optional[str]:
        """Get page ID by title.
        
        Args:
            title: Page title
            
        Returns:
            Page ID if found, None otherwise
        """
        url = f"{self.base_url}/rest/api/content"
        params = {
            "title": title,
            "spaceKey": self.space_key,
            "expand": "version"
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data["results"]:
                return data["results"][0]["id"]
            
            return None
            
        except requests.RequestException as e:
            print(f"❌ Error getting page ID: {e}")
            return None
    
    def create_or_update_page(self, title: str, content: str, parent_id: Optional[str] = None) -> bool:
        """Create or update a Confluence page.
        
        Args:
            title: Page title
            content: Page content in Confluence format
            parent_id: Parent page ID (optional)
            
        Returns:
            True if successful, False otherwise
        """
        page_id = self.get_page_id(title)
        
        if page_id:
            return self._update_page(page_id, title, content)
        else:
            return self._create_page(title, content, parent_id)
    
    def _create_page(self, title: str, content: str, parent_id: Optional[str] = None) -> bool:
        """Create a new Confluence page.
        
        Args:
            title: Page title
            content: Page content
            parent_id: Parent page ID
            
        Returns:
            True if successful, False otherwise
        """
        url = f"{self.base_url}/rest/api/content"
        
        page_data = {
            "type": "page",
            "title": title,
            "space": {"key": self.space_key},
            "body": {
                "storage": {
                    "value": content,
                    "representation": "storage"
                }
            }
        }
        
        if parent_id:
            page_data["ancestors"] = [{"id": parent_id}]
        
        try:
            response = self.session.post(url, json=page_data)
            response.raise_for_status()
            
            print(f"✅ Created page: {title}")
            return True
            
        except requests.RequestException as e:
            print(f"❌ Error creating page '{title}': {e}")
            return False
    
    def _update_page(self, page_id: str, title: str, content: str) -> bool:
        """Update an existing Confluence page.
        
        Args:
            page_id: Page ID
            title: Page title
            content: Page content
            
        Returns:
            True if successful, False otherwise
        """
        # Get current version
        url = f"{self.base_url}/rest/api/content/{page_id}"
        
        try:
            response = self.session.get(url, params={"expand": "version"})
            response.raise_for_status()
            
            current_data = response.json()
            current_version = current_data["version"]["number"]
            
            # Update page
            update_data = {
                "version": {"number": current_version + 1},
                "title": title,
                "type": "page",
                "body": {
                    "storage": {
                        "value": content,
                        "representation": "storage"
                    }
                }
            }
            
            response = self.session.put(url, json=update_data)
            response.raise_for_status()
            
            print(f"✅ Updated page: {title}")
            return True
            
        except requests.RequestException as e:
            print(f"❌ Error updating page '{title}': {e}")
            return False
    
    def push_documentation(self, docs_dir: Path) -> bool:
        """Push documentation from HTML directory to Confluence.
        
        Args:
            docs_dir: Directory containing HTML documentation
            
        Returns:
            True if successful, False otherwise
        """
        html_dir = docs_dir / "html" / "jenkins_job_manager"
        
        if not html_dir.exists():
            print(f"❌ Documentation directory not found: {html_dir}")
            return False
        
        # Find all HTML files
        html_files = list(html_dir.rglob("*.html"))
        
        if not html_files:
            print(f"❌ No HTML files found in: {html_dir}")
            return False
        
        print(f"📚 Found {len(html_files)} HTML files to push")
        
        # Create main documentation page
        main_index = html_dir / "index.html"
        if main_index.exists():
            with main_index.open("r", encoding="utf-8") as f:
                html_content = f.read()
            
            confluence_content = self.convert_html_to_confluence(html_content)
            
            # Add attribution
            confluence_content += """
            <hr/>
            <p><em>Documentation generated automatically by Jenkins Job Manager.</em></p>
            <p><a href="https://www.augmentcode.com/?utm_source=atlassian&utm_medium=confluence_page&utm_campaign=confluence">Co-authored by Augment Code</a></p>
            """
            
            success = self.create_or_update_page(
                "Jenkins Job Manager Documentation",
                confluence_content
            )
            
            if not success:
                return False
        
        print("✅ Documentation pushed to Confluence successfully!")
        return True


def main() -> None:
    """Main function for Confluence push script."""
    parser = argparse.ArgumentParser(description="Push documentation to Confluence")
    parser.add_argument("--confluence-url", required=True, help="Confluence base URL")
    parser.add_argument("--username", help="Confluence username (or set CONFLUENCE_USERNAME)")
    parser.add_argument("--api-token", help="Confluence API token (or set CONFLUENCE_API_TOKEN)")
    parser.add_argument("--space-key", required=True, help="Confluence space key")
    parser.add_argument("--docs-dir", type=Path, default=Path(__file__).parent, help="Documentation directory")
    
    args = parser.parse_args()
    
    # Get credentials from environment if not provided
    username = args.username or os.getenv("CONFLUENCE_USERNAME")
    api_token = args.api_token or os.getenv("CONFLUENCE_API_TOKEN")
    
    if not username:
        print("❌ Confluence username required (--username or CONFLUENCE_USERNAME env var)")
        sys.exit(1)
    
    if not api_token:
        print("❌ Confluence API token required (--api-token or CONFLUENCE_API_TOKEN env var)")
        sys.exit(1)
    
    # Create pusher and push documentation
    pusher = ConfluencePusher(args.confluence_url, username, api_token, args.space_key)
    
    success = pusher.push_documentation(args.docs_dir)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
