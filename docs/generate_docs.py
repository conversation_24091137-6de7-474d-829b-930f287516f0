#!/usr/bin/env python3
import shutil
import subprocess
import sys
from pathlib import Path


def main() -> None:
    """Generate HTML documentation using pdoc."""
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    docs_dir = script_dir
    html_dir = docs_dir / "html"
    
    print("🔧 Generating documentation with pdoc...")
    
    # Remove existing HTML documentation
    if html_dir.exists():
        print(f"📁 Removing existing documentation: {html_dir}")
        shutil.rmtree(html_dir)
    
    # Create HTML directory
    html_dir.mkdir(exist_ok=True)
    
    try:
        # Generate documentation using pdoc
        cmd = [
            sys.executable, "-m", "pdoc",
            "-o", str(html_dir),
            "--show-source",
            "jenkins_job_manager"
        ]
        
        print(f"🚀 Running: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            check=True
        )
        
        print("✅ Documentation generated successfully!")
        print(f"📖 Documentation available at: {html_dir / 'jenkins_job_manager' / 'index.html'}")
        
        # Print some statistics
        html_files = list(html_dir.rglob("*.html"))
        print(f"📊 Generated {len(html_files)} HTML files")
        
        # Show the main index file
        main_index = html_dir / "jenkins_job_manager.html"
        if main_index.exists():
            print(f"🌐 Main documentation: file://{main_index.absolute()}")
        else:
            # Try alternative location
            alt_index = html_dir / "index.html"
            if alt_index.exists():
                print(f"🌐 Main documentation: file://{alt_index.absolute()}")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error generating documentation: {e}")
        print(f"📝 stdout: {e.stdout}")
        print(f"📝 stderr: {e.stderr}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ pdoc not found. Install it with: pip install pdoc")
        sys.exit(1)


if __name__ == "__main__":
    main()
