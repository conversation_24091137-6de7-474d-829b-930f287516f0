#!/usr/bin/env python3
"""
<PERSON>ript to update Confluence with the latest Jenkins Job Manager documentation.
This script converts the markdown documentation to Confluence format and pushes it.
"""

import argparse
import os
import sys
from pathlib import Path
import markdown
from confluence_push import ConfluencePusher


def markdown_to_confluence_html(markdown_content: str) -> str:
    """Convert markdown content to HTML suitable for Confluence.
    
    Args:
        markdown_content: Markdown content to convert
        
    Returns:
        HTML content suitable for Confluence
    """
    # Convert markdown to HTML
    html = markdown.markdown(
        markdown_content,
        extensions=[
            'codehilite',
            'fenced_code',
            'tables',
            'toc'
        ]
    )
    
    # Add some Confluence-specific styling
    confluence_html = f"""
    <div class="confluence-content">
        {html}
    </div>
    
    <hr/>
    <div class="confluence-footer">
        <p><em>Documentation updated automatically by Jenkins Job Manager.</em></p>
        <p><strong>Last Updated:</strong> {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><a href="https://www.augmentcode.com/?utm_source=atlassian&utm_medium=confluence_page&utm_campaign=confluence">Co-authored by Augment Code</a></p>
    </div>
    """
    
    return confluence_html


def main():
    """Main function to update Confluence documentation."""
    parser = argparse.ArgumentParser(description="Update Confluence with JJM documentation")
    parser.add_argument("--confluence-url", required=True, help="Confluence base URL")
    parser.add_argument("--username", help="Confluence username (or set CONFLUENCE_USERNAME)")
    parser.add_argument("--api-token", help="Confluence API token (or set CONFLUENCE_API_TOKEN)")
    parser.add_argument("--space-key", required=True, help="Confluence space key")
    parser.add_argument("--page-title", default="Jenkins Job Manager Documentation", help="Page title")
    parser.add_argument("--markdown-file", type=Path, default=Path(__file__).parent / "confluence_update.md", help="Markdown file to convert")
    
    args = parser.parse_args()
    
    # Get credentials from environment if not provided
    username = args.username or os.getenv("CONFLUENCE_USERNAME")
    api_token = args.api_token or os.getenv("CONFLUENCE_API_TOKEN")
    
    if not username:
        print("❌ Confluence username required (--username or CONFLUENCE_USERNAME env var)")
        sys.exit(1)
    
    if not api_token:
        print("❌ Confluence API token required (--api-token or CONFLUENCE_API_TOKEN env var)")
        sys.exit(1)
    
    if not args.markdown_file.exists():
        print(f"❌ Markdown file not found: {args.markdown_file}")
        sys.exit(1)
    
    print(f"📖 Reading markdown file: {args.markdown_file}")
    
    # Read markdown content
    with args.markdown_file.open('r', encoding='utf-8') as f:
        markdown_content = f.read()
    
    print("🔄 Converting markdown to Confluence HTML...")
    
    # Convert to Confluence HTML
    confluence_html = markdown_to_confluence_html(markdown_content)
    
    print("🚀 Pushing to Confluence...")
    
    # Create pusher and push documentation
    pusher = ConfluencePusher(args.confluence_url, username, api_token, args.space_key)
    
    # Convert HTML to Confluence storage format
    confluence_content = pusher.convert_html_to_confluence(confluence_html)
    
    # Create or update the page
    success = pusher.create_or_update_page(args.page_title, confluence_content)
    
    if success:
        print("✅ Documentation successfully updated in Confluence!")
        print(f"📄 Page: {args.page_title}")
        print(f"🌐 URL: {args.confluence_url}/display/{args.space_key}")
    else:
        print("❌ Failed to update Confluence documentation")
        sys.exit(1)


if __name__ == "__main__":
    main()
