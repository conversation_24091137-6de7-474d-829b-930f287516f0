# Jenkins Job Manager (JJM) - Updated Documentation

## Overview

Jenkins Job Manager (JJM) is a Python-based command-line tool for managing Jenkins ML pipeline jobs with advanced features including sequential job execution, real-time status tracking, and job cancellation capabilities.

## Recent Updates (v2.0)

### 🔒 **Enhanced Security & Simplified Authentication**
- **Environment-Only Authentication**: Credentials are now exclusively read from environment variables
  - `JENKINS_USER` - Jenkins username
  - `JENKINS_API_TOKEN` - Jenkins API token
- **Removed**: Command-line credential parameters for improved security
- **Removed**: `--jenkins-url` CLI option (now config-file only)

### 🚀 **Advanced Job Management**
- **Sequential Execution**: Dataset jobs complete before training jobs start automatically
- **Job Tracking**: Each job gets a unique 8-character ID for easy reference
- **Real-time Status**: Monitor job progress with `jjm status`
- **Job Cancellation**: Cancel running or queued jobs with `jjm cancel <job_id>`

### 🧹 **Simplified API**
- **Removed**: Build with parameters functionality (cleaner, more reliable)
- **Simplified**: Job triggering without complex parameter passing
- **Enhanced**: Better error handling and user feedback

## Installation

```bash
pip install jenkins-job-manager
```

## Configuration

### Environment Variables (Required)
```bash
export JENKINS_USER="your-username"
export JENKINS_API_TOKEN="your-api-token"
```

### Configuration File
Create `conf/jjm_config.yml`:
```yaml
jenkins_url: "https://your-jenkins-server.com"
dataset_job: "dataset-creation-job"
training_job: "training-pipeline-job"
```

## Usage

### Basic Commands

#### Initialize Configuration
```bash
jjm init
```

#### Trigger Dataset Job Only
```bash
jjm trigger --dataset-only
```

#### Trigger Full Pipeline (Dataset → Training)
```bash
jjm trigger
```

#### Check Job Status
```bash
jjm status
```

#### Cancel a Job
```bash
jjm cancel <job_id>
```

### Advanced Features

#### Sequential Pipeline Execution
When triggering a full pipeline, JJM automatically:
1. Starts the dataset creation job
2. Waits for dataset job completion
3. Starts the training job only after dataset job succeeds
4. Provides real-time status updates

#### Job Status Monitoring
The `jjm status` command shows:
- Job ID and name
- Current status (queued, running, success, failure, etc.)
- Job type (dataset, training, full_pipeline)
- Creation and update timestamps
- Parent-child relationships for sequential jobs

#### Job Cancellation
Cancel any running or queued job:
```bash
jjm cancel abc12345
```

## Configuration Management

### Config Files
JJM supports multiple configuration files that are automatically merged:
- `query_config.yml` - Database query configurations
- `sp_config.yml` - Signal processing configurations  
- `training_config.yml` - Training pipeline configurations

### Config Commands
```bash
# View current configuration
jjm config

# Set configuration values
jjm config set jenkins_url https://new-jenkins.com
jjm config set dataset_job new-dataset-job
```

## API Reference

### Core Classes

#### JenkinsService
Main service class for job management:
- `execute_dataset_job()` → Returns job ID
- `execute_training_job()` → Returns job ID  
- `execute_full_pipeline()` → Returns pipeline job ID
- `get_job_status(job_id)` → Returns current status
- `cancel_job(job_id)` → Cancels job
- `list_jobs()` → Returns all tracked jobs

#### JobTracker
Manages job state and persistence:
- Tracks job metadata and status
- Provides job lifecycle management
- Handles job relationships (parent/child)

#### ConfigManager
Handles configuration file operations:
- Merges multiple config files
- Validates configuration
- Provides config hashing for duplicate detection

## Error Handling

JJM provides comprehensive error handling:
- **Authentication Errors**: Clear messages for missing credentials
- **Job Failures**: Detailed status reporting and failure reasons
- **Configuration Issues**: Validation and helpful error messages
- **Network Issues**: Retry logic and timeout handling

## Testing

JJM includes comprehensive test coverage (73% overall):
- **86 tests** covering all major functionality
- **Unit tests** for individual components
- **Integration tests** for end-to-end workflows
- **Mock testing** for external dependencies

Run tests:
```bash
pytest tests/ -v
```

## Migration from v1.x

### Breaking Changes
1. **Authentication**: Must use environment variables only
2. **Job Parameters**: No longer supported (jobs run with default configs)
3. **CLI Options**: `--jenkins-url` removed
4. **Return Values**: Job methods now return job IDs instead of boolean

### Migration Steps
1. Set environment variables for authentication
2. Update scripts to handle job ID return values
3. Remove any parameter passing to job triggers
4. Update configuration files if using custom Jenkins URLs

## Troubleshooting

### Common Issues

#### Authentication Failed
```bash
# Ensure environment variables are set
echo $JENKINS_USER
echo $JENKINS_API_TOKEN
```

#### Job Not Found
```bash
# Check job exists in Jenkins and config is correct
jjm config
```

#### Status Not Updating
```bash
# Check Jenkins connectivity and permissions
jjm status
```

## Support

For issues and feature requests, please contact your system administrator or check the internal documentation portal.

---

*Documentation generated automatically by Jenkins Job Manager v2.0*  
*Co-authored by [Augment Code](https://www.augmentcode.com/?utm_source=atlassian&utm_medium=confluence_page&utm_campaign=confluence)*
