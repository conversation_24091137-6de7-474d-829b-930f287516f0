# Jenkins Job Manager (JJM)

A simple Python CLI tool for triggering Jenkins jobs with configuration management and state tracking.

## Features

- **Simple CLI Interface**: Easy-to-use command-line tool for Jenkins job management
- **Configuration Management**: Simple YAML-based configuration
- **State Tracking**: Tracks configuration changes and prevents duplicate executions
- **ML Pipeline Support**: Support for dataset creation and training pipelines
- **Minimal Dependencies**: Only requires click, httpx, and pyyaml

## Installation

```bash
# Install from source
pip install -e .
```

## Quick Start

### Setup

```bash
# Run the setup script to initialize configuration
python setup_jjm.py

# Set up your Jenkins credentials
cp .env.template .env
# Edit .env with your Jenkins username and API token

# Configure Jenkins URL
jjm config set jenkins_url https://jenkins.example.com
```

### Basic Usage

```bash
# Trigger a full ML pipeline (dataset creation + training)
jjm trigger

# Trigger only dataset creation
jjm trigger --dataset-only

# Dry run to see what would be executed
jjm trigger --dry-run

# Initialize default configurations
jjm init-defaults
```

## Configuration

JJM uses a simple YAML configuration file stored at `~/.config/jjm/config.yml`:

```yaml
jenkins_url: https://jenkins.example.com
dataset_job: dataset-creation-job
training_job: training-pipeline-job
```

### Configuration Commands

```bash
# Show current configuration
jjm config show

# Set Jenkins URL
jjm config set jenkins_url https://jenkins.example.com

# Set job names
jjm config set dataset_job my-dataset-job
jjm config set training_job my-training-job
```

## ML Pipeline

JJM is designed for ML pipelines with two main jobs:

1. **Dataset Creation Job**: Merges `query_config.yml` and `sp_config.yml` files
2. **Training Pipeline Job**: Uses `training_config.yml` file

### Configuration Files

Place these files in your `conf/` directory:

- `query_config.yml`: Query configuration for dataset creation
- `sp_config.yml`: Signal processing configuration for dataset creation  
- `training_config.yml`: Training pipeline configuration

### State Tracking

JJM tracks configuration states to:
- Warn when using default configurations
- Detect duplicate executions with same config
- Prompt for approval when needed

State is stored in `~/.config/jjm/state.json` (outside your repository).

## Environment Variables

```bash
export JENKINS_USER="your-username"
export JENKINS_API_TOKEN="your-api-token"
```

## CLI Reference

### Commands

- `jjm trigger` - Trigger Jenkins jobs
- `jjm config` - Manage configuration
- `jjm init-defaults` - Initialize default config states
- `jjm version` - Show version

### Trigger Options

- `--jenkins-url URL` - Override configured Jenkins URL
- `--dataset-only` - Execute only dataset creation job
- `--dry-run` - Show what would be executed
- `--config-dir DIR` - Config directory (default: conf)
- `--target-path PATH` - Target path for merged config

## Development

### Project Structure

```
jenkins_job_manager/
├── cli/                    # Command-line interface
├── client/                 # Jenkins HTTP client
├── config/                 # Configuration management
├── service/                # Business logic
└── exceptions.py           # Custom exceptions
```

### Dependencies

- **click**: CLI framework
- **httpx**: HTTP client for Jenkins API
- **pyyaml**: YAML configuration files

### Testing

```bash
# Run tests
python -m pytest

# Run with coverage
python -m pytest --cov=jenkins_job_manager --cov-report=html
```

## License

MIT License - see LICENSE file for details.
