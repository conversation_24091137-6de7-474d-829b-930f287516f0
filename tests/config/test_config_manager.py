import tempfile
import yaml
from pathlib import Path

import pytest

from jen<PERSON>_job_manager.config.config_manager import ConfigManager


@pytest.fixture
def temp_config_dir():
    """Create a temporary directory for config files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def config_manager(temp_config_dir):
    """Create a ConfigManager instance with temporary config directory."""
    return ConfigManager(temp_config_dir)


@pytest.fixture
def sample_configs(temp_config_dir):
    """Create sample configuration files."""
    # Create query_config.yml
    query_config = {
        'database': {
            'host': 'localhost',
            'port': 5432,
            'name': 'test_db'
        },
        'query': {
            'limit': 1000,
            'timeout': 30
        }
    }

    query_file = temp_config_dir / 'query_config.yml'
    with query_file.open('w') as f:
        yaml.dump(query_config, f)

    # Create sp_config.yml
    sp_config = {
        'processing': {
            'batch_size': 100,
            'workers': 4
        },
        'output': {
            'format': 'json',
            'compression': 'gzip'
        }
    }

    sp_file = temp_config_dir / 'sp_config.yml'
    with sp_file.open('w') as f:
        yaml.dump(sp_config, f)

    # Create training_config.yml
    training_config = {
        'model': {
            'type': 'neural_network',
            'layers': [128, 64, 32]
        },
        'training': {
            'epochs': 100,
            'learning_rate': 0.001
        }
    }

    training_file = temp_config_dir / 'training_config.yml'
    with training_file.open('w') as f:
        yaml.dump(training_config, f)

    return {
        'query': query_config,
        'sp': sp_config,
        'training': training_config
    }


class TestConfigManager:
    """Test ConfigManager class."""

    def test_merge_configs(self, config_manager, sample_configs):
        """Test merging configuration files."""
        merged = config_manager.merge_configs(['query', 'sp'])

        # Should contain data from both files
        assert 'database' in merged
        assert 'query' in merged
        assert 'processing' in merged
        assert 'output' in merged

        # Check specific values
        assert merged['database']['host'] == 'localhost'
        assert merged['processing']['batch_size'] == 100

    def test_merge_nonexistent_config(self, config_manager):
        """Test merging with nonexistent config file."""
        merged = config_manager.merge_configs(['nonexistent'])
        assert merged == {}

    def test_merge_empty_config_list(self, config_manager):
        """Test merging empty config list."""
        merged = config_manager.merge_configs([])
        assert merged == {}

    def test_write_config(self, config_manager, temp_config_dir):
        """Test writing configuration to file."""
        config_data = {
            'test': {
                'value': 123,
                'name': 'test_config'
            }
        }

        target_path = temp_config_dir / 'output.yml'
        config_manager.write_config(config_data, str(target_path))

        # Verify file was created and contains correct data
        assert target_path.exists()

        with target_path.open('r') as f:
            loaded_data = yaml.safe_load(f)

        assert loaded_data == config_data

    def test_write_config_creates_directories(self, config_manager, temp_config_dir):
        """Test that write_config creates parent directories."""
        config_data = {'test': 'value'}
        target_path = temp_config_dir / 'subdir' / 'nested' / 'config.yml'

        config_manager.write_config(config_data, str(target_path))

        assert target_path.exists()
        assert target_path.parent.exists()

    def test_get_config_hash(self, config_manager, sample_configs):
        """Test getting configuration hash."""
        hash1 = config_manager.get_config_hash(['query', 'sp'])
        hash2 = config_manager.get_config_hash(['query', 'sp'])
        hash3 = config_manager.get_config_hash(['training'])

        # Same configs should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 12  # First 12 characters of SHA256

        # Different configs should produce different hash
        assert hash1 != hash3

    def test_get_config_hash_with_nonexistent_file(self, config_manager):
        """Test getting hash with nonexistent config file."""
        hash_value = config_manager.get_config_hash(['nonexistent'])
        assert len(hash_value) == 12
        assert hash_value != ""

    def test_config_exists(self, config_manager, sample_configs):
        """Test checking if config file exists."""
        assert config_manager.config_exists('query')
        assert config_manager.config_exists('sp')
        assert config_manager.config_exists('training')
        assert not config_manager.config_exists('nonexistent')

    def test_get_config_file_hash(self, config_manager, sample_configs):
        """Test getting hash of single config file."""
        hash1 = config_manager.get_config_file_hash('query')
        hash2 = config_manager.get_config_file_hash('query')
        hash3 = config_manager.get_config_file_hash('sp')

        # Same file should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 64  # Full SHA256 hash

        # Different files should produce different hashes
        assert hash1 != hash3

        # Nonexistent file should return None
        assert config_manager.get_config_file_hash('nonexistent') is None

    def test_validate_config_files(self, config_manager, sample_configs):
        """Test validating config files."""
        # Valid files should return empty list
        missing = config_manager.validate_config_files(['query', 'sp'])
        assert missing == []

        # Missing file should be reported
        missing = config_manager.validate_config_files(
            ['query', 'nonexistent'])
        assert len(missing) == 1
        assert 'nonexistent_config.yml (missing)' in missing

    def test_validate_invalid_yaml(self, config_manager, temp_config_dir):
        """Test validating config file with invalid YAML."""
        # Create invalid YAML file
        invalid_file = temp_config_dir / 'invalid_config.yml'
        with invalid_file.open('w') as f:
            f.write('invalid: yaml: content: [')

        missing = config_manager.validate_config_files(['invalid'])
        assert len(missing) == 1
        assert 'invalid_config.yml (invalid YAML)' in missing

    def test_merge_and_write(self, config_manager, sample_configs, temp_config_dir):
        """Test merge_and_write method."""
        target_path = temp_config_dir / 'merged_output.yml'

        config_hash = config_manager.merge_and_write(
            ['query', 'sp'],
            str(target_path)
        )

        # Should return hash
        assert len(config_hash) == 12

        # File should exist and contain merged data
        assert target_path.exists()

        with target_path.open('r') as f:
            merged_data = yaml.safe_load(f)

        assert 'database' in merged_data
        assert 'processing' in merged_data

    def test_merge_and_write_with_invalid_files(self, config_manager):
        """Test merge_and_write with invalid config files."""
        with pytest.raises(ValueError, match="Invalid config files"):
            config_manager.merge_and_write(['nonexistent'], '/tmp/output.yml')

    def test_merge_configs_with_overlapping_keys(self, config_manager, temp_config_dir):
        """Test merging configs with overlapping keys."""
        # Create two configs with overlapping keys
        config1 = {
            'shared': {'value1': 'from_config1', 'unique1': 'only_in_config1'},
            'unique_section1': {'data': 'config1'}
        }
        config2 = {
            'shared': {'value1': 'from_config2', 'unique2': 'only_in_config2'},
            'unique_section2': {'data': 'config2'}
        }

        config1_file = temp_config_dir / 'overlap1_config.yml'
        config2_file = temp_config_dir / 'overlap2_config.yml'

        with config1_file.open('w') as f:
            yaml.dump(config1, f)
        with config2_file.open('w') as f:
            yaml.dump(config2, f)

        merged = config_manager.merge_configs(['overlap1', 'overlap2'])

        # Later config should override earlier ones completely for nested dicts
        assert merged['shared']['value1'] == 'from_config2'
        assert merged['shared']['unique2'] == 'only_in_config2'
        # unique1 is not preserved because the entire 'shared' dict is replaced
        assert merged['unique_section1']['data'] == 'config1'
        assert merged['unique_section2']['data'] == 'config2'

    def test_write_config_error_handling(self, config_manager, temp_config_dir):
        """Test error handling in write_config."""
        config_data = {'test': 'value'}

        # Create a read-only directory to trigger permission error
        readonly_dir = temp_config_dir / 'readonly'
        readonly_dir.mkdir()
        readonly_dir.chmod(0o444)  # Read-only

        # Try to write to read-only directory (should raise ValueError)
        with pytest.raises(ValueError, match="Error writing config"):
            config_manager.write_config(
                config_data, str(readonly_dir / 'config.yml'))
