"""Tests for jenkins_job_manager.config.jjm_config module."""

import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from jenkins_job_manager.config.jjm_config import (
    get_config_file,
    load_config,
    save_config,
    get_jenkins_url,
    get_job_names,
)


class TestJJMConfig:
    """Test cases for JJM configuration functions."""
    
    def test_get_config_file(self) -> None:
        """Test config file path in conf directory."""
        config_file = get_config_file()
        assert str(config_file).endswith('conf/jjm_config.yml')
        assert config_file.parent.name == 'conf'
    
    def test_load_config_creates_default(self) -> None:
        """Test that load_config creates default config if none exists."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=Path(temp_dir) / 'jjm_config.yml'):
                config = load_config()

                assert config['jenkins_url'] == 'http://localhost:8080'
                assert config['dataset_job'] == 'dataset-creation-job'
                assert config['training_job'] == 'training-pipeline-job'

                # Check that config file was created
                config_file = Path(temp_dir) / 'jjm_config.yml'
                assert config_file.exists()
    
    def test_load_config_existing_file(self) -> None:
        """Test loading existing config file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'

            # Create a config file
            test_config = {
                'jenkins_url': 'https://jenkins.example.com',
                'dataset_job': 'my-dataset-job',
                'training_job': 'my-training-job'
            }

            with config_file.open('w') as f:
                import yaml
                yaml.dump(test_config, f)

            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                config = load_config()

                assert config['jenkins_url'] == 'https://jenkins.example.com'
                assert config['dataset_job'] == 'my-dataset-job'
                assert config['training_job'] == 'my-training-job'
    
    def test_save_config(self) -> None:
        """Test saving config to file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'
            test_config = {
                'jenkins_url': 'https://test.jenkins.com',
                'dataset_job': 'test-dataset',
                'training_job': 'test-training'
            }

            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                save_config(test_config)

                # Verify file was created and contains correct data
                assert config_file.exists()

                with config_file.open('r') as f:
                    import yaml
                    saved_config = yaml.safe_load(f)

                assert saved_config == test_config
    
    def test_get_jenkins_url_default(self) -> None:
        """Test getting Jenkins URL with default value."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'
            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                url = get_jenkins_url()
                assert url == 'http://localhost:8080'

    def test_get_jenkins_url_configured(self) -> None:
        """Test getting configured Jenkins URL."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'

            test_config = {'jenkins_url': 'https://custom.jenkins.com'}

            with config_file.open('w') as f:
                import yaml
                yaml.dump(test_config, f)

            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                url = get_jenkins_url()
                assert url == 'https://custom.jenkins.com'
    
    def test_get_job_names_default(self) -> None:
        """Test getting job names with default values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'
            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                dataset_job, training_job = get_job_names()
                assert dataset_job == 'dataset-creation-job'
                assert training_job == 'training-pipeline-job'

    def test_get_job_names_configured(self) -> None:
        """Test getting configured job names."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'

            test_config = {
                'dataset_job': 'custom-dataset-job',
                'training_job': 'custom-training-job'
            }

            with config_file.open('w') as f:
                import yaml
                yaml.dump(test_config, f)

            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                dataset_job, training_job = get_job_names()
                assert dataset_job == 'custom-dataset-job'
                assert training_job == 'custom-training-job'

    def test_load_config_empty_file(self) -> None:
        """Test loading config from empty file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / 'jjm_config.yml'
            config_file.touch()  # Create empty file

            with patch('jenkins_job_manager.config.jjm_config.get_config_file', return_value=config_file):
                config = load_config()

                # Should return empty dict for empty file
                assert config == {}
