import json
import tempfile
import time
from pathlib import Path
from unittest.mock import patch

import pytest

from jenkins_job_manager.client.jenkins_client import JobStatus
from jenkins_job_manager.service.job_tracker import JobTracker, TrackedJob


@pytest.fixture
def temp_state_dir():
    """Create a temporary directory for state files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def job_tracker(temp_state_dir):
    """Create a JobTracker instance with temporary state directory."""
    return JobTracker(temp_state_dir)


class TestTrackedJob:
    """Test TrackedJob dataclass."""
    
    def test_tracked_job_creation(self):
        """Test creating a TrackedJob."""
        job = TrackedJob(
            job_id="test-123",
            job_name="test-job",
            build_number=42,
            status=JobStatus.RUNNING,
            created_at=time.time(),
            updated_at=time.time(),
            config_hash="abc123",
            job_type="dataset"
        )
        
        assert job.job_id == "test-123"
        assert job.job_name == "test-job"
        assert job.build_number == 42
        assert job.status == JobStatus.RUNNING
        assert job.config_hash == "abc123"
        assert job.job_type == "dataset"
    
    def test_is_active(self):
        """Test is_active method."""
        running_job = TrackedJob(
            job_id="test-1", job_name="test", build_number=1,
            status=JobStatus.RUNNING, created_at=time.time(), updated_at=time.time()
        )
        queued_job = TrackedJob(
            job_id="test-2", job_name="test", build_number=2,
            status=JobStatus.QUEUED, created_at=time.time(), updated_at=time.time()
        )
        completed_job = TrackedJob(
            job_id="test-3", job_name="test", build_number=3,
            status=JobStatus.SUCCESS, created_at=time.time(), updated_at=time.time()
        )
        
        assert running_job.is_active()
        assert queued_job.is_active()
        assert not completed_job.is_active()
    
    def test_is_completed(self):
        """Test is_completed method."""
        success_job = TrackedJob(
            job_id="test-1", job_name="test", build_number=1,
            status=JobStatus.SUCCESS, created_at=time.time(), updated_at=time.time()
        )
        failure_job = TrackedJob(
            job_id="test-2", job_name="test", build_number=2,
            status=JobStatus.FAILURE, created_at=time.time(), updated_at=time.time()
        )
        running_job = TrackedJob(
            job_id="test-3", job_name="test", build_number=3,
            status=JobStatus.RUNNING, created_at=time.time(), updated_at=time.time()
        )
        
        assert success_job.is_completed()
        assert failure_job.is_completed()
        assert not running_job.is_completed()
    
    def test_to_dict_from_dict(self):
        """Test serialization and deserialization."""
        original = TrackedJob(
            job_id="test-123",
            job_name="test-job",
            build_number=42,
            status=JobStatus.RUNNING,
            created_at=1234567890.0,
            updated_at=1234567891.0,
            config_hash="abc123",
            job_type="dataset"
        )
        
        # Convert to dict and back
        data = original.to_dict()
        restored = TrackedJob.from_dict(data)
        
        assert restored.job_id == original.job_id
        assert restored.job_name == original.job_name
        assert restored.build_number == original.build_number
        assert restored.status == original.status
        assert restored.created_at == original.created_at
        assert restored.updated_at == original.updated_at
        assert restored.config_hash == original.config_hash
        assert restored.job_type == original.job_type


class TestJobTracker:
    """Test JobTracker class."""
    
    def test_create_job(self, job_tracker):
        """Test creating a new job."""
        job_id = job_tracker.create_job(
            job_name="test-job",
            build_number=42,
            config_hash="abc123",
            job_type="dataset"
        )
        
        assert job_id is not None
        assert len(job_id) == 8  # Short UUID
        
        job = job_tracker.get_job(job_id)
        assert job is not None
        assert job.job_name == "test-job"
        assert job.build_number == 42
        assert job.config_hash == "abc123"
        assert job.job_type == "dataset"
        assert job.status == JobStatus.QUEUED
    
    def test_update_job_status(self, job_tracker):
        """Test updating job status."""
        job_id = job_tracker.create_job("test-job")
        
        # Update status
        success = job_tracker.update_job_status(job_id, JobStatus.RUNNING, build_number=123)
        assert success
        
        job = job_tracker.get_job(job_id)
        assert job.status == JobStatus.RUNNING
        assert job.build_number == 123
    
    def test_update_nonexistent_job(self, job_tracker):
        """Test updating a job that doesn't exist."""
        success = job_tracker.update_job_status("nonexistent", JobStatus.RUNNING)
        assert not success
    
    def test_get_all_jobs(self, job_tracker):
        """Test getting all jobs."""
        # Create multiple jobs
        job_id1 = job_tracker.create_job("job-1", job_type="dataset")
        job_id2 = job_tracker.create_job("job-2", job_type="training")
        
        jobs = job_tracker.get_all_jobs()
        assert len(jobs) == 2
        
        job_ids = [job.job_id for job in jobs]
        assert job_id1 in job_ids
        assert job_id2 in job_ids
    
    def test_get_active_jobs(self, job_tracker):
        """Test getting active jobs."""
        # Create jobs with different statuses
        active_id = job_tracker.create_job("active-job")
        completed_id = job_tracker.create_job("completed-job")
        
        job_tracker.update_job_status(active_id, JobStatus.RUNNING)
        job_tracker.update_job_status(completed_id, JobStatus.SUCCESS)
        
        active_jobs = job_tracker.get_active_jobs()
        assert len(active_jobs) == 1
        assert active_jobs[0].job_id == active_id
    
    def test_get_jobs_by_type(self, job_tracker):
        """Test getting jobs by type."""
        dataset_id = job_tracker.create_job("dataset-job", job_type="dataset")
        training_id = job_tracker.create_job("training-job", job_type="training")
        
        dataset_jobs = job_tracker.get_jobs_by_type("dataset")
        training_jobs = job_tracker.get_jobs_by_type("training")
        
        assert len(dataset_jobs) == 1
        assert len(training_jobs) == 1
        assert dataset_jobs[0].job_id == dataset_id
        assert training_jobs[0].job_id == training_id
    
    def test_remove_job(self, job_tracker):
        """Test removing a job."""
        job_id = job_tracker.create_job("test-job")
        
        # Verify job exists
        assert job_tracker.get_job(job_id) is not None
        
        # Remove job
        success = job_tracker.remove_job(job_id)
        assert success
        
        # Verify job is gone
        assert job_tracker.get_job(job_id) is None
    
    def test_remove_nonexistent_job(self, job_tracker):
        """Test removing a job that doesn't exist."""
        success = job_tracker.remove_job("nonexistent")
        assert not success
    
    def test_cleanup_old_jobs(self, job_tracker):
        """Test cleaning up old jobs."""
        # Create old completed job
        old_time = time.time() - (35 * 24 * 60 * 60)  # 35 days ago
        job_id = job_tracker.create_job("old-job")
        
        # Manually set old timestamps
        job = job_tracker.get_job(job_id)
        job.created_at = old_time
        job.updated_at = old_time
        job.status = JobStatus.SUCCESS
        job_tracker._save_jobs()
        
        # Create recent job
        recent_id = job_tracker.create_job("recent-job")
        
        # Cleanup jobs older than 30 days
        removed_count = job_tracker.cleanup_old_jobs(max_age_days=30)
        
        assert removed_count == 1
        assert job_tracker.get_job(job_id) is None
        assert job_tracker.get_job(recent_id) is not None
    
    def test_get_job_summary(self, job_tracker):
        """Test getting job summary."""
        # Create jobs with different statuses
        job_tracker.create_job("job-1")  # QUEUED by default
        
        job_id2 = job_tracker.create_job("job-2")
        job_tracker.update_job_status(job_id2, JobStatus.RUNNING)
        
        job_id3 = job_tracker.create_job("job-3")
        job_tracker.update_job_status(job_id3, JobStatus.SUCCESS)
        
        summary = job_tracker.get_job_summary()
        
        assert summary.get('queued', 0) == 1
        assert summary.get('running', 0) == 1
        assert summary.get('success', 0) == 1
    
    def test_persistence(self, temp_state_dir):
        """Test that jobs are persisted across instances."""
        # Create first tracker and add job
        tracker1 = JobTracker(temp_state_dir)
        job_id = tracker1.create_job("persistent-job", job_type="test")
        
        # Create second tracker and verify job exists
        tracker2 = JobTracker(temp_state_dir)
        job = tracker2.get_job(job_id)
        
        assert job is not None
        assert job.job_name == "persistent-job"
        assert job.job_type == "test"
