"""Tests for jenkins_job_manager.service.jenkins_service module."""

import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import pytest

from jenkins_job_manager.client.jenkins_client import JobStatus
from jenkins_job_manager.service.jenkins_service import JenkinsService


class TestJenkinsService:
    """Test cases for JenkinsService class."""

    @patch('jenkins_job_manager.service.jenkins_service.JobTracker')
    @patch('jenkins_job_manager.service.jenkins_service.ConfigManager')
    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_service_initialization(self, mock_load_config, mock_jenkins_client, mock_config_manager, mock_job_tracker) -> None:
        """Test Jenkins service initialization."""
        mock_load_config.return_value = {
            'jenkins_url': 'https://jenkins.example.com',
            'dataset_job': 'dataset-job',
            'training_job': 'training-job'
        }

        service = JenkinsService()

        assert service.jenkins_url == 'https://jenkins.example.com'
        assert service.dataset_job == 'dataset-job'
        assert service.training_job == 'training-job'
        mock_jenkins_client.assert_called_once_with(
            'https://jenkins.example.com')
        mock_config_manager.assert_called_once()
        mock_job_tracker.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_get_config_hash(self, mock_load_config, mock_jenkins_client) -> None:
        """Test config hash generation."""
        mock_load_config.return_value = {}

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            (config_dir / 'query_config.yml').write_text('query: test')
            (config_dir / 'sp_config.yml').write_text('sp: test')

            service = JenkinsService(config_dir=str(config_dir))
            hash_value = service._get_config_hash(['query', 'sp'])

            assert isinstance(hash_value, str)
            assert len(hash_value) == 12  # Truncated SHA256

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_merge_configs(self, mock_load_config, mock_jenkins_client) -> None:
        """Test config file merging."""
        mock_load_config.return_value = {}

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            with (config_dir / 'query_config.yml').open('w') as f:
                f.write('query:\n  param1: value1\n')

            with (config_dir / 'sp_config.yml').open('w') as f:
                f.write('sp:\n  param2: value2\n')

            service = JenkinsService(config_dir=str(config_dir))
            merged = service._merge_configs(['query', 'sp'])

            assert 'query' in merged
            assert 'sp' in merged
            assert merged['query']['param1'] == 'value1'
            assert merged['sp']['param2'] == 'value2'

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_execute_dataset_job_dry_run(self, mock_load_config, mock_jenkins_client) -> None:
        """Test dataset job execution in dry run mode."""
        mock_load_config.return_value = {'dataset_job': 'test-dataset-job'}

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            (config_dir / 'query_config.yml').write_text('query: test')
            (config_dir / 'sp_config.yml').write_text('sp: test')

            service = JenkinsService(config_dir=str(config_dir))

            # Mock user input to approve execution
            with patch('builtins.input', return_value='y'):
                result = service.execute_dataset_job(dry_run=True)

            assert result == "dry-run-dataset"
            # Should not trigger actual job in dry run
            mock_jenkins_client.return_value.trigger_job.assert_not_called()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_execute_dataset_job_success(self, mock_load_config, mock_jenkins_client) -> None:
        """Test successful dataset job execution."""
        mock_load_config.return_value = {'dataset_job': 'test-dataset-job'}
        mock_client = Mock()
        mock_client.trigger_job.return_value = 123  # Return build number
        mock_jenkins_client.return_value = mock_client

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            (config_dir / 'query_config.yml').write_text('query: test')
            (config_dir / 'sp_config.yml').write_text('sp: test')

            service = JenkinsService(config_dir=str(config_dir))

            # Mock user input to approve execution
            with patch('builtins.input', return_value='y'):
                result = service.execute_dataset_job()

            assert result is not None  # Should return job ID
            mock_client.trigger_job.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_execute_training_job_success(self, mock_load_config, mock_jenkins_client) -> None:
        """Test successful training job execution."""
        mock_load_config.return_value = {'training_job': 'test-training-job'}
        mock_client = Mock()
        mock_client.trigger_job.return_value = 456  # Return build number
        mock_jenkins_client.return_value = mock_client

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config file
            (config_dir / 'training_config.yml').write_text('training: test')

            service = JenkinsService(config_dir=str(config_dir))

            # Mock user input to approve execution
            with patch('builtins.input', return_value='y'):
                result = service.execute_training_job()

            assert result is not None  # Should return job ID
            mock_client.trigger_job.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_execute_full_pipeline_success(self, mock_load_config, mock_jenkins_client) -> None:
        """Test successful full pipeline execution."""
        mock_load_config.return_value = {
            'dataset_job': 'test-dataset-job',
            'training_job': 'test-training-job'
        }
        mock_client = Mock()
        mock_client.trigger_job.return_value = 789  # Return build number
        mock_client.get_build_status.return_value = JobStatus.SUCCESS
        mock_jenkins_client.return_value = mock_client

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            (config_dir / 'query_config.yml').write_text('query: test')
            (config_dir / 'sp_config.yml').write_text('sp: test')
            (config_dir / 'training_config.yml').write_text('training: test')

            service = JenkinsService(config_dir=str(config_dir))

            # Mock user input to approve execution and job completion waiting
            with patch('builtins.input', return_value='y'), \
                    patch.object(service, '_wait_for_job_completion', return_value=True):
                result = service.execute_full_pipeline()

            assert result is not None  # Should return pipeline job ID
            assert mock_client.trigger_job.call_count == 2  # Dataset + training

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_init_defaults(self, mock_load_config, mock_jenkins_client) -> None:
        """Test initializing default configurations."""
        mock_load_config.return_value = {}

        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)

            # Create test config files
            (config_dir / 'query_config.yml').write_text('query: test')
            (config_dir / 'sp_config.yml').write_text('sp: test')
            (config_dir / 'training_config.yml').write_text('training: test')

            service = JenkinsService(config_dir=str(config_dir))
            service.init_defaults()

            # Check that state was saved
            state = service._load_state()
            assert 'defaults' in state
            assert 'query' in state['defaults']
            assert 'sp' in state['defaults']
            assert 'training' in state['defaults']

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_context_manager(self, mock_load_config, mock_jenkins_client) -> None:
        """Test Jenkins service as context manager."""
        mock_load_config.return_value = {}
        mock_client = Mock()
        mock_jenkins_client.return_value = mock_client

        with JenkinsService() as service:
            assert isinstance(service, JenkinsService)

        # Should call close on client
        mock_client.close.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JobTracker')
    @patch('jenkins_job_manager.service.jenkins_service.ConfigManager')
    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_execute_dataset_job_returns_job_id(self, mock_load_config, mock_jenkins_client, mock_config_manager, mock_job_tracker) -> None:
        """Test that execute_dataset_job returns job ID."""
        mock_load_config.return_value = {
            'jenkins_url': 'https://jenkins.example.com',
            'dataset_job': 'dataset-job',
            'training_job': 'training-job'
        }

        # Mock client to return build number
        mock_client = Mock()
        mock_client.trigger_job.return_value = 123
        mock_jenkins_client.return_value = mock_client

        # Mock job tracker
        mock_tracker = Mock()
        mock_tracker.create_job.return_value = "test-job-id"
        mock_job_tracker.return_value = mock_tracker

        # Mock config manager
        mock_manager = Mock()
        mock_manager.get_config_hash.return_value = "abc123"
        mock_manager.merge_configs.return_value = {"test": "config"}
        mock_config_manager.return_value = mock_manager

        with tempfile.TemporaryDirectory() as temp_dir:
            service = JenkinsService(config_dir=temp_dir)

            # Mock the approval prompt to return True
            with patch.object(service, '_prompt_approval', return_value=True):
                job_id = service.execute_dataset_job(dry_run=False)

        assert job_id == "test-job-id"
        mock_tracker.create_job.assert_called_once()
        mock_client.trigger_job.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JobTracker')
    @patch('jenkins_job_manager.service.jenkins_service.ConfigManager')
    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_cancel_job(self, mock_load_config, mock_jenkins_client, mock_config_manager, mock_job_tracker) -> None:
        """Test job cancellation."""
        mock_load_config.return_value = {}

        # Mock client
        mock_client = Mock()
        mock_client.cancel_build.return_value = True
        mock_jenkins_client.return_value = mock_client

        # Mock job tracker with a job
        mock_job = Mock()
        mock_job.job_name = "test-job"
        mock_job.build_number = 123

        mock_tracker = Mock()
        mock_tracker.get_job.return_value = mock_job
        mock_job_tracker.return_value = mock_tracker

        with tempfile.TemporaryDirectory() as temp_dir:
            service = JenkinsService(config_dir=temp_dir)

            success = service.cancel_job("test-job-id")

        assert success
        mock_client.cancel_build.assert_called_once_with("test-job", 123)
        mock_tracker.update_job_status.assert_called_once_with(
            "test-job-id", JobStatus.ABORTED)

    @patch('jenkins_job_manager.service.jenkins_service.JobTracker')
    @patch('jenkins_job_manager.service.jenkins_service.ConfigManager')
    @patch('jenkins_job_manager.service.jenkins_service.JenkinsClient')
    @patch('jenkins_job_manager.service.jenkins_service.load_config')
    def test_refresh_all_job_statuses(self, mock_load_config, mock_jenkins_client, mock_config_manager, mock_job_tracker) -> None:
        """Test refreshing all job statuses from Jenkins."""
        mock_load_config.return_value = {}

        # Mock client
        mock_client = Mock()
        mock_client.get_build_status.return_value = JobStatus.SUCCESS
        mock_client.get_last_build_number.return_value = 123
        mock_jenkins_client.return_value = mock_client

        # Mock job tracker with jobs
        mock_job1 = Mock()
        mock_job1.job_id = "job1"
        mock_job1.job_name = "test-job-1"
        mock_job1.build_number = 123

        mock_job2 = Mock()
        mock_job2.job_id = "job2"
        mock_job2.job_name = "test-job-2"
        mock_job2.build_number = None  # No build number yet

        mock_tracker = Mock()
        mock_tracker.get_all_jobs.return_value = [mock_job1, mock_job2]
        mock_job_tracker.return_value = mock_tracker

        with tempfile.TemporaryDirectory() as temp_dir:
            service = JenkinsService(config_dir=temp_dir)

            service.refresh_all_job_statuses()

        # Should update status for job with build number
        assert mock_client.get_build_status.called
        # Should try to get last build for job without build number
        mock_client.get_last_build_number.assert_called_with("test-job-2")
        # Should update job tracker
        assert mock_tracker.update_job_status.call_count >= 1

        # Verify the correct calls were made
        build_status_calls = mock_client.get_build_status.call_args_list
        assert any(call[0] == ("test-job-1", 123)
                   for call in build_status_calls)
