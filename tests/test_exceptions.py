"""Tests for jenkins_job_manager.exceptions module."""

import pytest

from jenkins_job_manager.exceptions import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    JJMConfigError,
    JJMAuthenticationError,
    JJMConnectionError,
    JJ<PERSON>PI<PERSON>r,
    J<PERSON><PERSON><PERSON>ob<PERSON>r,
    JJMValidation<PERSON>rror,
    J<PERSON><PERSON>ileError,
    JJMTimeoutError,
)


class TestJJMError:
    """Test cases for the base JJMError exception."""
    
    def test_basic_error(self) -> None:
        """Test basic error creation and string representation."""
        error = JJMError("Test error message")
        
        assert str(error) == "Test error message"
        assert error.message == "Test error message"
        assert error.details == {}
        assert error.cause is None
    
    def test_error_with_details(self) -> None:
        """Test error with additional details."""
        details = {"key": "value", "number": 42}
        error = JJMError("Test error", details=details)
        
        assert error.details == details
        assert "Details: {'key': 'value', 'number': 42}" in str(error)
    
    def test_error_with_cause(self) -> None:
        """Test error with underlying cause."""
        cause = ValueError("Original error")
        error = JJMError("Test error", cause=cause)
        
        assert error.cause is cause
    
    def test_error_inheritance(self) -> None:
        """Test that JJMError inherits from Exception."""
        error = JJMError("Test error")
        assert isinstance(error, Exception)


class TestJJMAPIError:
    """Test cases for JJMAPIError with HTTP-specific functionality."""
    
    def test_api_error_basic(self) -> None:
        """Test basic API error creation."""
        error = JJMAPIError("API request failed")
        
        assert error.message == "API request failed"
        assert error.status_code is None
        assert error.response_body is None
    
    def test_api_error_with_http_details(self) -> None:
        """Test API error with HTTP status and response body."""
        error = JJMAPIError(
            "API request failed",
            status_code=404,
            response_body="Not found",
        )
        
        assert error.status_code == 404
        assert error.response_body == "Not found"
        
        error_str = str(error)
        assert "Status: 404" in error_str
        assert "Response: Not found" in error_str
    
    def test_api_error_long_response_truncation(self) -> None:
        """Test that long response bodies are truncated in string representation."""
        long_response = "x" * 300
        error = JJMAPIError("API error", response_body=long_response)
        
        error_str = str(error)
        assert "..." in error_str
        assert len(error_str) < len(long_response) + 100  # Should be significantly shorter


class TestSpecificExceptions:
    """Test cases for specific exception types."""
    
    def test_config_error(self) -> None:
        """Test JJMConfigError creation."""
        error = JJMConfigError("Configuration invalid")
        assert isinstance(error, JJMError)
        assert error.message == "Configuration invalid"
    
    def test_authentication_error(self) -> None:
        """Test JJMAuthenticationError creation."""
        error = JJMAuthenticationError("Auth failed")
        assert isinstance(error, JJMError)
        assert error.message == "Auth failed"
    
    def test_connection_error(self) -> None:
        """Test JJMConnectionError creation."""
        error = JJMConnectionError("Connection failed")
        assert isinstance(error, JJMError)
        assert error.message == "Connection failed"
    
    def test_job_error(self) -> None:
        """Test JJMJobError creation."""
        error = JJMJobError("Job failed")
        assert isinstance(error, JJMError)
        assert error.message == "Job failed"
    
    def test_validation_error(self) -> None:
        """Test JJMValidationError creation."""
        error = JJMValidationError("Validation failed")
        assert isinstance(error, JJMError)
        assert error.message == "Validation failed"
    
    def test_file_error(self) -> None:
        """Test JJMFileError creation."""
        error = JJMFileError("File operation failed")
        assert isinstance(error, JJMError)
        assert error.message == "File operation failed"
    
    def test_timeout_error(self) -> None:
        """Test JJMTimeoutError creation."""
        error = JJMTimeoutError("Operation timed out")
        assert isinstance(error, JJMError)
        assert error.message == "Operation timed out"


class TestExceptionChaining:
    """Test exception chaining and cause handling."""
    
    def test_exception_chaining(self) -> None:
        """Test that exceptions can be properly chained."""
        original = ValueError("Original error")
        
        try:
            raise original
        except ValueError as e:
            chained = JJMError("Wrapped error", cause=e)
            assert chained.cause is e
    
    def test_nested_exception_details(self) -> None:
        """Test complex exception with multiple details."""
        details = {
            "operation": "job_trigger",
            "job_name": "test-job",
            "parameters": {"branch": "main"},
        }
        
        error = JJMJobError(
            "Failed to trigger job",
            details=details,
            cause=ConnectionError("Network unreachable"),
        )
        
        assert error.details["operation"] == "job_trigger"
        assert error.details["job_name"] == "test-job"
        assert isinstance(error.cause, ConnectionError)
