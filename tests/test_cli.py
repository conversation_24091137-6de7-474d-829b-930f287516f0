"""Tests for jenkins_job_manager.cli module."""

from unittest.mock import Mock, patch

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from jenkins_job_manager.cli.main import app


class TestCLI:
    """Test cases for CLI functionality."""

    def test_app_help(self) -> None:
        """Test CLI help output."""
        runner = Cli<PERSON>unner()
        result = runner.invoke(app, ["--help"])

        assert result.exit_code == 0
        assert "Jenkins Job Manager" in result.output
        assert "trigger" in result.output

    def test_app_version(self) -> None:
        """Test CLI version output."""
        runner = CliRunner()
        result = runner.invoke(app, ["version"])

        assert result.exit_code == 0
        assert "Jenkins Job Manager" in result.output
        assert "version" in result.output

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsService')
    def test_trigger_command_dry_run(self, mock_service_class) -> None:
        """Test trigger command in dry run mode."""
        mock_service = Mock()
        mock_service.execute_dataset_job.return_value = True
        mock_service_class.return_value.__enter__.return_value = mock_service

        runner = <PERSON><PERSON><PERSON>unner()
        result = runner.invoke(app, ["trigger", "--dry-run", "--dataset-only"])

        assert result.exit_code == 0
        mock_service.execute_dataset_job.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsService')
    def test_trigger_command_full_pipeline(self, mock_service_class) -> None:
        """Test trigger command for full pipeline."""
        mock_service = Mock()
        mock_service.execute_full_pipeline.return_value = True
        mock_service_class.return_value.__enter__.return_value = mock_service

        runner = CliRunner()
        result = runner.invoke(app, ["trigger"])

        assert result.exit_code == 0
        mock_service.execute_full_pipeline.assert_called_once()

    @patch('jenkins_job_manager.service.jenkins_service.JenkinsService')
    def test_init_defaults_command(self, mock_service_class) -> None:
        """Test init-defaults command."""
        mock_service = Mock()
        mock_service_class.return_value = mock_service

        runner = CliRunner()
        result = runner.invoke(app, ["init-defaults"])

        assert result.exit_code == 0
        mock_service.init_defaults.assert_called_once()

    def test_config_show_command(self) -> None:
        """Test config show command."""
        with patch('jenkins_job_manager.config.jjm_config.load_config') as mock_load:
            mock_load.return_value = {
                'jenkins_url': 'https://test.jenkins.com',
                'dataset_job': 'test-dataset',
                'training_job': 'test-training'
            }

            runner = CliRunner()
            result = runner.invoke(app, ["config", "show"])

            assert result.exit_code == 0
            assert "https://test.jenkins.com" in result.output

    def test_config_set_command(self) -> None:
        """Test config set command."""
        with patch('jenkins_job_manager.config.jjm_config.load_config') as mock_load, \
             patch('jenkins_job_manager.config.jjm_config.save_config') as mock_save:

            mock_load.return_value = {}

            runner = CliRunner()
            result = runner.invoke(app, ["config", "set", "jenkins_url", "https://new.jenkins.com"])

            assert result.exit_code == 0
            mock_save.assert_called_once()
            saved_config = mock_save.call_args[0][0]
            assert saved_config['jenkins_url'] == 'https://new.jenkins.com'

