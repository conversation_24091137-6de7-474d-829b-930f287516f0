import sys
from typing import Optional

import click

from jenkins_job_manager import __version__


def handle_error(error: Exception) -> None:
    """Handle and display errors."""
    print(f"Error: {error}")
    sys.exit(1)


@click.group()
@click.version_option(version=__version__, prog_name="jjm")
def app() -> None:
    """
    Jenkins Job Manager

    Environment Variables:\n
        JENKINS_USER: Jenkins username for authentication\n
        JENKINS_API_TOKEN: Jenkins API token for authentication\n
    """
    pass


@app.command()
@click.option("--dataset-only", is_flag=True, help="Execute only dataset creation job")
@click.option("--dry-run", "-n", is_flag=True, help="Show what would be executed")
@click.option("--config-dir", "-c", default="conf", help="Config directory (default: conf)")
@click.option("--target-path", default="/tmp/parameters.yml", help="Target config path")
def trigger(dataset_only: bool, dry_run: bool, config_dir: str, target_path: str) -> None:
    """Trigger Jenkins jobs for ML pipeline execution."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        with JenkinsService(config_dir=config_dir) as service:
            if dataset_only:
                job_id = service.execute_dataset_job(target_path, dry_run)
            else:
                job_id = service.execute_full_pipeline(target_path, dry_run)

            if not job_id:
                sys.exit(1)

    except Exception as e:
        handle_error(e)


@app.command()
@click.option("--config-dir", "-c", default="conf", help="Config directory")
def init_defaults(config_dir: str) -> None:
    """Initialize current configurations as default baseline."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        service = JenkinsService(config_dir=config_dir)
        service.init_defaults()

    except Exception as e:
        handle_error(e)


@app.group()
def config() -> None:
    """Manage JJM configuration."""
    pass


@config.command("show")
def config_show() -> None:
    """Show current configuration."""
    try:
        from jenkins_job_manager.config.jjm_config import load_config, get_config_file

        config = load_config()
        config_file = get_config_file()

        print(f"Configuration file: {config_file}")
        print(f"Jenkins URL: {config.get('jenkins_url', 'Not set')}")
        print(
            f"Dataset job: {config.get('dataset_job', 'dataset-creation-job')}")
        print(
            f"Training job: {config.get('training_job', 'training-pipeline-job')}")

    except Exception as e:
        handle_error(e)


@config.command("set")
@click.argument("key")
@click.argument("value")
def config_set(key: str, value: str) -> None:
    """
    Set a configuration value.

    Examples:
        jjm config set jenkins_url https://jenkins.example.com
        jjm config set dataset_job my-dataset-job
    """
    try:
        from jenkins_job_manager.config.jjm_config import load_config, save_config

        config = load_config()
        config[key] = value
        save_config(config)

        print(f"✅ Set {key} = {value}")

    except Exception as e:
        handle_error(e)


@app.command()
@click.option("--config-dir", "-c", default="conf", help="Config directory")
@click.option("--no-refresh", is_flag=True, help="Skip refreshing status from Jenkins API")
def status(config_dir: str, no_refresh: bool) -> None:
    """Show status of tracked jobs."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService
        from datetime import datetime

        service = JenkinsService(config_dir=config_dir)

        # Refresh job statuses from Jenkins API unless --no-refresh is specified
        if not no_refresh:
            print("🔄 Refreshing job statuses from Jenkins...")
            service.refresh_all_job_statuses()

        jobs = service.list_jobs()

        if not jobs:
            print("No tracked jobs found.")
            return

        print("📋 Tracked Jobs Status:")
        print("=" * 80)

        for job in sorted(jobs, key=lambda x: x.created_at, reverse=True):
            created_time = datetime.fromtimestamp(
                job.created_at).strftime("%Y-%m-%d %H:%M:%S")
            updated_time = datetime.fromtimestamp(
                job.updated_at).strftime("%Y-%m-%d %H:%M:%S")

            status_emoji = {
                'queued': '⏳',
                'running': '🔄',
                'success': '✅',
                'failure': '❌',
                'aborted': '🛑',
                'unstable': '⚠️',
                'unknown': '❓'
            }.get(job.status.value, '❓')

            print(f"{status_emoji} Job ID: {job.job_id}")
            print(f"   Name: {job.job_name}")
            print(f"   Type: {job.job_type or 'unknown'}")
            print(f"   Status: {job.status.value}")
            if job.build_number:
                print(f"   Build: #{job.build_number}")
            if job.parent_job_id:
                print(f"   Parent: {job.parent_job_id}")
            print(f"   Created: {created_time}")
            print(f"   Updated: {updated_time}")
            print()

        summary = service.job_tracker.get_job_summary()
        print("📊 Summary:")
        for status, count in summary.items():
            emoji = {
                'queued': '⏳',
                'running': '🔄',
                'success': '✅',
                'failure': '❌',
                'aborted': '🛑',
                'unstable': '⚠️',
                'unknown': '❓'
            }.get(status, '❓')
            print(f"   {emoji} {status}: {count}")

    except Exception as e:
        handle_error(e)


@app.command()
@click.argument("job_id")
@click.option("--config-dir", "-c", default="conf", help="Config directory")
def cancel(job_id: str, config_dir: str) -> None:
    """Cancel a running or queued job."""
    try:
        from jenkins_job_manager.service.jenkins_service import JenkinsService

        with JenkinsService(config_dir=config_dir) as service:
            success = service.cancel_job(job_id)
            if not success:
                sys.exit(1)

    except Exception as e:
        handle_error(e)


@app.command()
def version() -> None:
    """Show version information."""
    print(f"Jenkins Job Manager (jjm) version {__version__}")


if __name__ == "__main__":
    app()
