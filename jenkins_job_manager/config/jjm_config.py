from pathlib import Path
from typing import Dict

import yaml


def get_config_file() -> Path:
    """Get the JJM configuration file path in conf/ directory."""
    config_file = Path('conf') / 'jjm_config.yml'

    # Ensure conf directory exists
    config_file.parent.mkdir(exist_ok=True)

    return config_file


def load_config() -> Dict:
    """Load JJM configuration from conf/jjm_config.yml."""
    config_file = get_config_file()

    if not config_file.exists():
        # Create default config
        default_config = {
            'jenkins_url': 'http://localhost:8080',
            'dataset_job': 'dataset-creation-job',
            'training_job': 'training-pipeline-job'
        }
        save_config(default_config)
        return default_config

    with config_file.open('r') as f:
        return yaml.safe_load(f) or {}


def save_config(config: Dict) -> None:
    """Save JJM configuration to conf/jjm_config.yml."""
    config_file = get_config_file()

    with config_file.open('w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)


def get_jenkins_url() -> str:
    """Get Jenkins URL from config."""
    config = load_config()
    return config.get('jenkins_url', 'http://localhost:8080')


def get_job_names() -> tuple[str, str]:
    """Get dataset and training job names from config."""
    config = load_config()
    dataset_job = config.get('dataset_job', 'dataset-creation-job')
    training_job = config.get('training_job', 'training-pipeline-job')
    return dataset_job, training_job
