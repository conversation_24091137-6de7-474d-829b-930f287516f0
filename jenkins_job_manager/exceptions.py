from typing import Any, Dict, Optional


class JJMError(Exception):
    """Base exception class for all Jenkins Job Manager errors.
    
    This is the root exception that all other JJM exceptions inherit from.
    It provides a consistent interface for error handling throughout the package.
    
    Args:
        message: Human-readable error message
        details: Optional dictionary containing additional error context
        cause: Optional underlying exception that caused this error
    """

    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ) -> None:
        """Initialize the JJMError with message and optional context."""
        super().__init__(message)
        self.message = message
        self.details = details or {}
        self.cause = cause

    def __str__(self) -> str:
        """Return a string representation of the error."""
        if self.details:
            return f"{self.message} (Details: {self.details})"
        return self.message


class JJMConfigError(JJMError):
    """Exception raised for configuration-related errors.
    
    This includes validation failures, missing configuration files,
    invalid configuration values, and file handling errors.
    """
    pass


class JJMAuthenticationError(JJMError):
    """Exception raised for authentication-related errors.
    
    This includes missing credentials, invalid API tokens,
    and authentication failures with <PERSON>.
    """
    pass


class JJMConnectionError(JJMError):
    """Exception raised for network and connection-related errors.
    
    This includes network timeouts, connection failures,
    and unreachable Jenkins servers.
    """
    pass


class JJMAPIError(JJMError):
    """Exception raised for Jenkins API-related errors.
    
    This includes HTTP errors, invalid API responses,
    and Jenkins server errors.
    """

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ) -> None:
        """Initialize the JJMAPIError with HTTP-specific context.
        
        Args:
            message: Human-readable error message
            status_code: HTTP status code from the failed request
            response_body: Response body from the failed request
            details: Optional dictionary containing additional error context
            cause: Optional underlying exception that caused this error
        """
        super().__init__(message, details, cause)
        self.status_code = status_code
        self.response_body = response_body

    def __str__(self) -> str:
        """Return a string representation of the API error."""
        parts = [self.message]
        if self.status_code:
            parts.append(f"Status: {self.status_code}")
        if self.response_body:
            # Truncate long response bodies for readability
            body = self.response_body[:200] + "..." if len(self.response_body) > 200 else self.response_body
            parts.append(f"Response: {body}")
        if self.details:
            parts.append(f"Details: {self.details}")
        return " | ".join(parts)


class JJMJobError(JJMError):
    """Exception raised for job-specific errors.
    
    This includes job not found, job execution failures,
    and invalid job parameters.
    """
    pass


class JJMValidationError(JJMError):
    """Exception raised for data validation errors.
    
    This includes invalid input parameters, malformed configuration data,
    and schema validation failures.
    """
    pass


class JJMFileError(JJMError):
    """Exception raised for file operation errors.
    
    This includes file not found, permission errors,
    and file system operation failures.
    """
    pass


class JJMTimeoutError(JJMError):
    """Exception raised for timeout-related errors.
    
    This includes operation timeouts, job execution timeouts,
    and network request timeouts.
    """
    pass
